import re
import time
from ast import literal_eval
from functools import wraps
from typing import Callable, TypeVar, Any, Optional

import pandas as pd
from common.logger import logger

# Type variables for better type hinting
T = TypeVar('T')
DF = TypeVar('DF', bound=pd.DataFrame)


def timeit(func: Callable[..., T]) -> Callable[..., T]:
    """Decorator that logs the execution time of the decorated function.
    
    Args:
        func: The function to be timed
        
    Returns:
        A wrapped function that logs execution time
    """
    @wraps(func)
    def timeit_wrapper(*args: Any, **kwargs: Any) -> T:
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        total_time = end_time - start_time
        logger.debug(
            f"Execution of the function {func.__name__} took {total_time:.4f} seconds"
        )
        return result

    return timeit_wrapper


def lowercase_column_names(func: Callable[..., DF]) -> Callable[..., DF]:
    """Decorator that converts all DataFrame column names to lowercase.
    
    This decorator is designed to be used with functions that return a pandas DataFrame.
    It automatically converts all column names to lowercase after the function executes.
    
    Args:
        func: The function that returns a DataFrame
        
    Returns:
        A wrapped function that returns a DataFrame with lowercase column names
    """
    @wraps(func)
    def wrapper(*args: Any, **kwargs: Any) -> DF:
        df = func(*args, **kwargs)
        if isinstance(df, pd.DataFrame):
            df.columns = [col.lower() for col in df.columns]
        return df

    return wrapper

# Solution pattern regular expressions
# Pattern for cloud enterprise solutions
CLOUD_ENTERPRISE_PATTERN = r'^BP_CLD_ENTPR_S4CLD\d{4}_[A-Z0-9]{2}V\d{2}$'
# Pattern for on-premise HANA solutions
ONPREM_HANA_PATTERN = r'^BP_S4BL_S4HANAX_[A-Z0-9]{2}V1$'


# Pre-compile regex patterns for better performance
_cloud_pattern = re.compile(CLOUD_ENTERPRISE_PATTERN)
_onprem_pattern = re.compile(ONPREM_HANA_PATTERN)


def filter_solution(df: pd.DataFrame, column_name: str) -> pd.DataFrame:
    """Filter DataFrame to keep only rows with the most common solution pattern.
    
    Although SQL queries already filter by solution pattern, each system may contain
    solutions that match the pattern but are not needed. This function applies a second
    filter using regex patterns and keeps only solutions matching the most common pattern.
    
    Args:
        df: DataFrame containing solution data
        column_name: Name of the column containing solution identifiers
        
    Returns:
        DataFrame filtered to contain only rows with the most common solution pattern
        
    Raises:
        ValueError: If both patterns are equally common in the data
    """
    # Initialize counters for each pattern
    cloud_pattern_count = 0
    onprem_pattern_count = 0

    # Count occurrences of each pattern in the column
    for value in df[column_name].dropna():
        if _cloud_pattern.match(value):
            cloud_pattern_count += 1
        elif _onprem_pattern.match(value):
            onprem_pattern_count += 1

    # Determine the most common pattern
    if cloud_pattern_count > onprem_pattern_count:
        selected_pattern = _cloud_pattern
        pattern_name = "Cloud Enterprise"
    elif onprem_pattern_count > cloud_pattern_count:
        selected_pattern = _onprem_pattern
        pattern_name = "On-Premise HANA"
    else:
        # If both patterns are equally common, raise an exception
        raise ValueError("Both solution patterns are equally common in the data.")
    
    logger.info(f"Selected {pattern_name} pattern with {max(cloud_pattern_count, onprem_pattern_count)} matches")
    
    # Filter DataFrame to keep only rows matching the selected pattern
    filtered_df = df[df[column_name].apply(
        lambda x: bool(selected_pattern.match(x)) if pd.notnull(x) else False
    )]

    return filtered_df
