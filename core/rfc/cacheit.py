import os
import pandas as pd
from functools import wraps
from datetime import datetime
from common.logger import logger
from common.paths import paths
from common.config import get_cache_expiration_seconds


def cacheit(func):
    @wraps(func)
    def wrapper(instance):
        # Create a unique cache filename based on sys_client and function name
        __cache_filename = f"{instance.sys_client}___{func.__name__}.cache"
        __cache_filepath = os.path.join(paths.cache_dir, __cache_filename)

        # Check if the cache file exists and is not expired
        if os.path.exists(__cache_filepath):
            # Get file creation time
            file_creation_time = datetime.fromtimestamp(os.path.getctime(__cache_filepath))
            current_time = datetime.now()
            time_diff = (current_time - file_creation_time).total_seconds()
            
            # Check if cache is expired
            if time_diff > get_cache_expiration_seconds():
                logger.info(f"Cache file {__cache_filename} has expired, will be recreated.")
                try:
                    os.remove(__cache_filepath)
                except OSError as e:
                    logger.warning(f"Failed to remove expired cache file {__cache_filename}: {e}")
            else:
                try:
                    # If the cache file exists and is not expired, read the data from the file and return it
                    logger.info(f"The cache file {__cache_filename} is valid, skip RFC query.")
                    _df = pd.read_csv(__cache_filepath, na_filter=False)

                    logger.info(f'Read {_df.shape[0]} entries from {os.path.basename(__cache_filepath)}')
                    return _df
                except pd.errors.EmptyDataError:
                    # Log a warning if the cache file is empty
                    logger.warning(f"The file {__cache_filepath} is empty.")
                    try:
                        os.remove(__cache_filepath)
                    except OSError as e:
                        logger.warning(f"Failed to remove empty cache file {__cache_filename}: {e}")

        # If the cache file does not exist or is expired, execute the function, save the result to the cache file, and return it
        __df = func(instance)

        # Ensure cache directory exists
        os.makedirs(paths.cache_dir, exist_ok=True)
        __df.to_csv(__cache_filepath, index=False, na_rep='')
        logger.info(f'Created new cache file {__cache_filename} with {__df.shape[0]} entries')
        return __df

    return wrapper
