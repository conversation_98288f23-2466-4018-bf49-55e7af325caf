#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Perforce File Download Module

This module provides a simple interface for downloading files from Perforce server.
Main features:
1. Download files for a specific country code
2. Download files for multiple country codes
3. Automatic Perforce connection and error handling
4. Recursive directory download with all subdirectories
"""

import sys
import subprocess
from pathlib import Path
from typing import List, Union, Optional
from dataclasses import dataclass
from P4 import P4, P4Exception
import yaml

# Import project modules
from common.logger import logger
from common.paths import paths

@dataclass
class PerforceConfig:
    """Perforce configuration class"""
    port: str
    user: str
    depot_path: str

    @classmethod
    def from_config(cls) -> 'PerforceConfig':
        """Load configuration from config.yaml"""
        config_path = Path(__file__).parent.parent / 'config' / 'config.yaml'
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        perforce_config = config['perforce']
        return cls(
            port=perforce_config['port'],
            user=perforce_config['user'],
            depot_path=perforce_config['depot_path']
        )

class PerforceDownloader:
    """Perforce file downloader class"""
    
    def __init__(self, config: Optional[PerforceConfig] = None):
        """
        Initialize downloader
        
        Args:
            config: Perforce configuration, if None will load from config.yaml
        """
        self.config = config or PerforceConfig.from_config()
        self.p4 = P4()
        self.temp_client = None
        self._setup_p4()
        self._create_temp_client()
        
    def _setup_p4(self):
        """Setup Perforce connection"""
        self.p4.port = self.config.port
        self.p4.user = self.config.user
        self.p4.connect()
        self.p4.run_login()

    def _create_temp_client(self):
        """Create a temporary client for downloading files"""
        import uuid
        from datetime import datetime
        
        temp_client = f"temp_{self.config.user}_{uuid.uuid4().hex[:8]}"
        
        try:
            # Create client spec from scratch
            client_spec = {
                'Client': temp_client,
                'Owner': self.config.user,
                'Root': str(Path(paths.vfile_dir).absolute()),
                'View': [f"{self.config.depot_path}/... //{temp_client}/..."],
                'Description': f"Temporary client for file download. Created at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            }
            
            # Create the client
            self.p4.save_client(client_spec)
            logger.info(f"Created temporary client: {temp_client}")
            self.temp_client = temp_client
            self.p4.client = temp_client
            
        except P4Exception as e:
            logger.error(f"Failed to create temporary client: {e}")
            raise

    def _cleanup_temp_client(self):
        """Clean up temporary client"""
        if not self.temp_client:
            return

        try:
            # Try to delete the client
            self.p4.run_client('-d', '-f', self.temp_client)
            logger.info(f"Deleted temporary client: {self.temp_client}")
        except P4Exception as e:
            logger.warning(f"Could not delete temporary client {self.temp_client}. It will expire automatically.")
            logger.warning(f"Error details: {e}")
        finally:
            self.temp_client = None
            
    def download_country_files(self, country_code: str, target_dir: Optional[Union[str, Path]] = None) -> bool:
        """
        Download files for a specific country code
        
        Args:
            country_code: Country code
            target_dir: Target directory, if None will use default directory
            
        Returns:
            bool: Whether download was successful
        """
        # Set target directory
        if target_dir is None:
            target_dir = Path(paths.vfile_dir) / country_code
        else:
            target_dir = Path(target_dir) / country_code
            
        # Build Perforce path
        perforce_path = f"{self.config.depot_path}/{country_code}"
        
        logger.info(f"Starting download for {country_code} from {perforce_path} to {target_dir}")
        
        try:
            # Ensure target directory exists
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # First, get all files in the directory and its subdirectories
            files = self.p4.run_files(f"{perforce_path}/...")
            
            if not files:
                logger.warning(f"No files found in {perforce_path}")
                return True

            # Force sync all files and subdirectories recursively
            self.p4.run_sync("-f", f"{perforce_path}/...")
            
            logger.info(f"Successfully downloaded files for {country_code}")
            return True
            
        except P4Exception as e:
            logger.error(f"Failed to download files for {country_code}: {e}")
            return False
            
        except Exception as e:
            logger.error(f"Unknown error occurred while downloading files for {country_code}: {e}")
            return False
            
    def download_multiple_countries(self, country_codes: List[str], target_dir: Optional[Union[str, Path]] = None) -> dict:
        """
        Download files for multiple countries
        
        Args:
            country_codes: List of country codes
            target_dir: Target directory, if None will use default directory
            
        Returns:
            dict: Dictionary containing download results for each country
        """
        try:
            results = {}
            for country_code in country_codes:
                success = self.download_country_files(country_code, target_dir)
                results[country_code] = success
            return results
        finally:
            self._cleanup_temp_client()

    def __enter__(self):
        """Context manager entry"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self._cleanup_temp_client()

def download_files(country_codes: Union[str, List[str]], target_dir: Optional[Union[str, Path]] = None) -> Union[bool, dict]:
    """
    Convenience function: Download files
    
    Args:
        country_codes: Single country code or list of country codes
        target_dir: Target directory, if None will use default directory
        
    Returns:
        If single country code is provided, returns bool indicating success
        If list of country codes is provided, returns dict with results for each country
    """
    with PerforceDownloader() as downloader:
        if isinstance(country_codes, str):
            return downloader.download_country_files(country_codes, target_dir)
        else:
            return downloader.download_multiple_countries(country_codes, target_dir)

def check_p4_environment() -> bool:
    """
    Check if Perforce environment is properly configured
    
    Returns:
        bool: Whether environment is correct
    """
    try:
        # Check if p4 command is available
        subprocess.run(["p4", "-V"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
        
        # Check if p4 is logged in
        subprocess.run(["p4", "info"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
        
        return True
    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        logger.error(f"Perforce environment check failed: {e}")
        return False

def download_all_country_files() -> bool:
    """
    Download files for all countries defined in config.yaml
    
    Returns:
        bool: Whether all downloads were successful
    """
    # Check environment
    if not check_p4_environment():
        return False
        
    # Get all country codes and download
    from common.config import get_all_country_codes
    country_codes = get_all_country_codes()
    results = download_files(country_codes)
    
    # Output results
    success_count = sum(1 for success in results.values() if success)
    total_count = len(results)
    
    if success_count == total_count:
        logger.info("All files downloaded successfully")
        return True
    else:
        logger.warning(f"{total_count - success_count} countries failed to download")
        return False

if __name__ == "__main__":
    success = download_all_country_files()
    sys.exit(0 if success else 1)