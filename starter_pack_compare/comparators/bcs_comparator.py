from typing import Dict, List
import pandas as pd

from .base_comparator import BaseComparator

class BCSComparator(BaseComparator):
    """Comparator for BCS (Business Configuration Set) files.
    
    This class handles the comparison of BCS files across different countries,
    using the BC field definition to determine keys for comparison.
    """
    
    def __init__(self, bc_field_definition: pd.DataFrame):
        """Initialize the BCS comparator.
        
        Args:
            bc_field_definition: DataFrame containing BC field definitions
        """
        super().__init__()
        self.bc_field_definition = bc_field_definition

    def get_keys(self, activity_id: str) -> List[str]:
        """Get keys for a specific BCS activity.
        
        Args:
            activity_id: BCS activity ID
            
        Returns:
            List of keys for the activity
        """
        mask = (
            (self.bc_field_definition['bcset_id'] == activity_id) &
            (self.bc_field_definition['keyflag'] == 'X')
        )
        return self.bc_field_definition[mask]['param'].tolist()

    def get_non_keys(self, activity_id: str) -> List[str]:
        """Get non-key fields for a specific BCS activity.
        
        Args:
            activity_id: BCS activity ID
            
        Returns:
            List of non-key fields for the activity
        """
        mask = (
            (self.bc_field_definition['bcset_id'] == activity_id) &
            (self.bc_field_definition['keyflag'] == '')
        )
        return self.bc_field_definition[mask]['param'].tolist()

    def compare_files(self, bcs_records: pd.DataFrame) -> None:
        """Compare BCS files across different countries.
        
        Args:
            bcs_records: DataFrame containing BCS file information
        """
        # Initialize HTML output
        self.init_html_output('BCS')
        # Get DE records as reference
        de_records = bcs_records[bcs_records['land1'] == self.reference_country]
        
        # Process each DE record
        for _, de_row in de_records.iterrows():
            # Get keys for this combination
            keys = self.get_keys(de_row['activity_id'])
            
            if not keys:
                continue
                
            # Read DE file content
            de_file_path = self.get_file_path(self.reference_country, de_row['filename'])
            de_content = self.read_file_content(de_file_path)
            
            if not de_content:
                continue
                
            # Find matching records in other countries
            de_bbid_base = self.get_base_bbid(de_row['bbid'])
            matching_records = bcs_records[
                (bcs_records['bbid'].str.extract(r'^(.+?)\s*\(', expand=False) == de_bbid_base) &
                (bcs_records['activity_type'] == de_row['activity_type']) &
                (bcs_records['activity_id'] == de_row['activity_id']) &
                (bcs_records['land1'].isin(self.other_countries))
            ]
            
            if matching_records.empty:
                continue
                
            self.print_comparison_header(
                'BCS',
                de_row['bbid'],
                de_row['activity_type'],
                de_row['activity_id'],
                de_row['filename'],
                keys
            )
            
            # Compare with each matching record
            for _, other_row in matching_records.iterrows():
                other_file_path = self.get_file_path(other_row['land1'], other_row['filename'])
                other_content = self.read_file_content(other_file_path)
                
                if not other_content:
                    continue
                
                # First check if key values match
                key_values_match = True
                for key in keys:
                    if key not in de_content or key not in other_content:
                        key_values_match = False
                        break
                    if not self.is_equal(de_content[key], other_content[key], other_row['land1']):
                        key_values_match = False
                        break
                
                if not key_values_match:
                    continue
                
                self.print_matching_files(
                    de_row['filename'],
                    other_row['land1'],
                    other_row['filename'],
                    other_row['bbid']
                )
                
                # Compare all other fields and add to HTML
                non_keys = self.get_non_keys(de_row['activity_id'])
                self.print_field_differences_with_html(
                    de_content, other_content, other_row['land1'], keys, non_keys,
                    de_row['bbid'], de_row['activity_type'], de_row['activity_id'],
                    de_row['filename'], other_row['filename'], other_row['bbid']
                )
        
        # Save HTML output at the end
        self.save_html_output()