# Starter Pack Comparison Tool

## Prerequisites

### pyrfc Installation
Due to certain limitations, pyrfc cannot be installed directly via pip. 
For more details, please refer to https://pypi.org/project/pyrfc/2.5.0/

```bash
# Download wheel file from https://github.com/SAP/PyRFC/releases
pip install pyrfc-2.5.0-cp39-cp39-win_amd64.whl
```

## Quick Start
```bash
git clone https://github.wdf.sap.corp/I072162/starter-pack-comparison.git
cd starter-pack-compare
pip install -r requirements.txt

python main.py
```

## Configuration
Edit `config/config.yaml` for:
- Country comparison settings
- Deprecated scope items
- Perforce settings
- Cache expiration settings

SAP connection parameters are now configured in `config/system_client.yaml`.

## Data Directory Structure
The `data` directory is used to store various project-related data:
- `data/vfile/`: Contains files downloaded from Perforce, organized by country code.
- `data/output/`: Stores generated reports and comparison results.

## Instance Rules Configuration
The `config/inst_rules.yaml` file defines rules for expected differences in comparison results. It contains a mapping of country codes or other identifiers to a list of expected differences. This helps in filtering out known discrepancies during the comparison process.
