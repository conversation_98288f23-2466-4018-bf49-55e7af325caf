import pandas
import pandas as pd
from common.logger import logger
from core.rfc.rfc_conn import RfcConn

# SAP Data Dictionary table for field definitions
DD03L_TAB = 'DD03L'
# Required fields from DD03L table
DD03L_FLD = ['TABNAME', 'FIE<PERSON><PERSON><PERSON>', 'POSIT<PERSON>', 'K<PERSON><PERSON><PERSON><PERSON>', 'LENG']
# Fields to exclude from table metadata
EXCLUDED_FIELDS = ['.INCLUDE', '.INCLU--AP']  # Include and append statements
# Using non-printable character as delimiter instead of pipe character
# to avoid conflicts with data containing pipe characters
DELIMITER = "\x01"  # ASCII SOH (Start of Heading) character

class RfcQuery:
    """SAP RFC Query class for executing RFC_READ_TABLE calls.
    
    This class provides methods to query SAP tables using RFC_READ_TABLE function module,
    with support for pagination, field filtering, and result conversion to pandas DataFrames.
    """
    def __init__(self, sys_client):
        """Initialize RFC Query with the specified SAP system client.
        
        Args:
            sys_client: SAP system client identifier
        """
        self.conn = RfcConn(sys_client).conn
        self.sys_client = sys_client

    def set_attribute(self, attr_name, value):
        """Set a dynamic attribute on this instance.
        
        Args:
            attr_name: Name of the attribute to set
            value: Value to assign to the attribute
        """
        setattr(self, attr_name, value)
        logger.debug(f'{self.sys_client}: the attribute {attr_name} has been set')

    def get_attribute(self, attr_name):
        """Get the value of a dynamic attribute.
        
        Args:
            attr_name: Name of the attribute to retrieve
            
        Returns:
            The attribute value or default message if attribute doesn't exist
        """
        return getattr(self, attr_name, "attribute does not exist")

    def has_attribute(self, attr_name):
        """Check if a dynamic attribute exists.
        
        Args:
            attr_name: Name of the attribute to check
            
        Returns:
            Boolean indicating whether the attribute exists
        """
        return hasattr(self, attr_name)

    def reconnect(self):
        """Reconnect to the SAP system.
        
        Workaround for memory issues in systems without load balancing (e.g., HC4).
        Closes and reopens the connection for each page of data retrieval.
        """
        self.conn.close()
        self.conn.open()

    def _query(self, fields: str | list, sql_table: str, where: str | list = '', maxrows: int = 0, fromrow: int = 0,
               no_data: str = ''):
        """Execute a query against SAP using RFC_READ_TABLE function module.
        
        This is an internal method that handles the low-level RFC call and result processing.
        
        Args:
            fields: List of field names to retrieve or a single field name
            sql_table: SAP table name to query
            where: WHERE clause conditions as string or list of strings
            maxrows: Maximum number of rows to return (0 = unlimited)
            fromrow: Starting row index for pagination
            no_data: Special flag for query behavior ('C' for count only)
            
        Returns:
            Tuple containing (data_entries, field_headers)
            - data_entries: 2D list of query results
            - field_headers: List of field names
            
        Raises:
            Exception: Any error that occurs during the RFC call
        """
        # Format fields for RFC call - each field must be in a dict with FIELDNAME key
        fields = [{'FIELDNAME': x} for x in fields]

        # Format WHERE conditions for RFC call - each condition in a dict with TEXT key
        options = [{'TEXT': x} for x in where]

        # Set maximum rows to return (improves performance for test queries)
        rowcount = maxrows

        # Execute the RFC_READ_TABLE function call
        try:
            result = self.conn.call("RFC_READ_TABLE", 
                                   QUERY_TABLE=sql_table, 
                                   DELIMITER=DELIMITER, 
                                   FIELDS=fields,
                                   OPTIONS=options, 
                                   ROWCOUNT=rowcount, 
                                   ROWSKIPS=fromrow, 
                                   GET_SORTED='X',
                                   USE_ET_DATA_4_RETURN='X', 
                                   NO_DATA=no_data)
        except Exception as e:
            # Re-raise the exception to be handled by caller
            raise

        # Process the query results
        entries = []
        
        # Extract data and field metadata from result
        data_fields = result["ET_DATA"]  # Data rows
        data_names = result["FIELDS"]    # Field metadata

        # Extract field headers from metadata
        headers = [x['FIELDNAME'] for x in data_names]
        length_fields = len(data_fields)

        # Extract and clean each data row
        for line in range(0, length_fields):
            entries.append(data_fields[line]["LINE"].strip())

        # Split each row by the delimiter and strip whitespace from each value
        entries = [list(map(str.strip, x.strip().split(DELIMITER))) for x in entries]

        return entries, headers

    def query_as_df(self, fields, sql_table, where='', maxrows=0, fromrow=0, batch_size=100000) -> pandas.DataFrame:
        """Execute a query and return results as a pandas DataFrame with lowercase column names.
        
        This method handles pagination for large result sets by fetching data in batches.
        It supports SQL-like syntax with '*' for all fields and automatically converts
        field names to uppercase for SAP compatibility.
        
        Args:
            fields: List of field names to retrieve or '*' for all fields
            sql_table: SAP table name to query
            where: WHERE clause conditions as string or list of strings
            maxrows: Maximum number of rows to return (0 = unlimited)
            fromrow: Starting row index for pagination
            batch_size: Number of rows to fetch in each batch
            
        Returns:
            pandas.DataFrame: Query results with lowercase column names
            
        Raises:
            Exception: Any error that occurs during the query execution
        """
        # Convert field names to uppercase for SAP compatibility
        fields = [__field.upper() for __field in fields]

        # Check for WHERE clause length limitation
        if len(where) > 72:
            logger.error(f'The length of where clause exceed 72 chars')
            logger.error(f'{where}')

        # Handle '*' wildcard for all fields
        if fields[0] == '*' or fields == '*':
            _field = self._get_all_fields(sql_table)
        else:
            _field = fields

        # Get total count of matching records
        _count = self.query_count(fields, sql_table, where=where, maxrows=maxrows, fromrow=fromrow)

        # Initialize list to store DataFrames from each batch
        _dataframes = []

        # Fetch data in batches to handle large result sets
        _is_last_batch_logged = False
        for start_index in range(0, _count, batch_size):
            # Reconnect for each batch to avoid memory issues
            self.reconnect()
            
            # Log batch retrieval information
            logger.info(
                f'{self.sys_client}: fetch data from the table {sql_table}, '
                f'for the index range {start_index} - {min(start_index + batch_size - 1, _count - 1)}')

            # Execute query for current batch
            _entries, _field_list = self._query(_field, sql_table, where, maxrows=batch_size, fromrow=start_index,
                                                no_data='')
            _df_batch = pd.DataFrame(_entries, columns=_field_list)

            # Add batch results to the list
            _dataframes.append(_df_batch)

            # Check if this is the last batch
            if _df_batch.shape[0] < batch_size or start_index + _df_batch.shape[0] == _count:
                _is_last_batch_logged = True
                last_index = start_index + _df_batch.shape[0] - 1
                logger.info(
                    f'{self.sys_client}: last batch fetched from the table {sql_table}, '
                    f'for the index range {start_index} - {last_index}')
                break  # Exit loop if this is the last batch

        # Log completion if not already logged by the last batch
        if not _is_last_batch_logged:
            logger.info(f'{self.sys_client}: data retrieval from the table {sql_table} completed')

        # Combine all batch DataFrames into a single DataFrame
        _combined_df = pd.concat(_dataframes, ignore_index=True)

        # Convert column names to lowercase for consistency
        _combined_df.columns = [col.lower() for col in _combined_df.columns]

        return _combined_df

    def query_count(self, fields, sql_table, where='', maxrows=0, fromrow=0) -> int:
        """Get the count of records matching the query criteria.
        
        This method uses the NO_DATA='C' option of RFC_READ_TABLE to efficiently
        retrieve only the count without fetching actual data.
        
        Args:
            fields: List of field names or '*' for all fields
            sql_table: SAP table name to query
            where: WHERE clause conditions as string or list of strings
            maxrows: Maximum number of rows to count (0 = unlimited)
            fromrow: Starting row index
            
        Returns:
            int: Number of records matching the query criteria
        """
        # Handle '*' wildcard for all fields
        if fields[0] == '*' or fields == '*':
            _fields = self._get_all_fields(sql_table)
        else:
            _fields = fields

        # Execute count-only query (NO_DATA='C')
        _entries, _ = self._query(_fields, sql_table, where, maxrows, fromrow, no_data='C')
        _count = int(_entries[0][0])
        
        logger.info(f'{self.sys_client}: a total of {_count} entries have been detected in {sql_table}'
                    f' with given conditions.')

        return _count

    def _get_field_info(self, table):
        """Get field metadata for a SAP table from DD03L.
        
        Retrieves field definitions including name, position, key flag, and length
        for the specified table from the SAP Data Dictionary.
        
        Args:
            table: SAP table name
            
        Returns:
            list: List of field metadata records sorted by position
        """
        # Query DD03L for active field definitions
        where = [f'TABNAME = \'{table}\' AND AS4LOCAL = \'A\' AND AS4VERS = \'0000\'']
        field_info, _ = self._query(DD03L_FLD, DD03L_TAB, where, 0, 0)

        # Sort fields by position
        field_info = sorted(field_info, key=lambda x: x[2], reverse=False)

        return field_info

    def _get_all_fields(self, table, no_include=True):
        """Get all field names for a SAP table.
        
        Retrieves a list of all field names for the specified table,
        optionally excluding special include fields.
        
        Args:
            table: SAP table name
            no_include: Whether to exclude special include fields (default: True)
            
        Returns:
            list: List of field names sorted by position
        """
        # Get field metadata
        field_info = self._get_field_info(table)
        
        # Extract field names, filtering out excluded fields if requested
        all_fields = [elem[1] for elem in field_info if no_include and elem[1] not in EXCLUDED_FIELDS]

        return all_fields


if __name__ == '__main__':
    # Example usage of RfcQuery class
    _rfc_query = RfcQuery('R8K.079')
    
    # Query all fields from BB_MASTER table and print the resulting DataFrame
    result_df = _rfc_query.query_as_df('*', '/SMB/BB_MASTER')
    print(f"Retrieved {len(result_df)} rows with {len(result_df.columns)} columns")
    print(result_df.head())
