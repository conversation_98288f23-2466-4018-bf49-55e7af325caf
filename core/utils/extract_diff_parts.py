from typing import Tuple, Optional


def extract_diff_parts(s1: str, s2: str) -> Tu<PERSON>[str, str]:
    """Extract the different parts between two strings.
    
    This function identifies and extracts the differing portions between two strings
    by finding common prefixes and suffixes, then isolating the unique middle sections.
    
    Args:
        s1: First string to compare
        s2: Second string to compare
        
    Returns:
        A tuple containing the differing parts of s1 and s2 respectively
        
    Examples:
        >>> extract_diff_parts("I_BUKRS(D06,V_1010)", "I_BUKRS(D06,V_7Y10)")
        ('10', '7Y')
        >>> extract_diff_parts("ABC123XYZ", "DEF123XYZ")
        ('ABC', 'DEF')
        >>> extract_diff_parts("File_v1.data", "File_v2.data")
        ('v1', 'v2')
    """
    # Helper function: Find the length of the common suffix
    def find_common_suffix_length(str1: str, str2: str) -> int:
        """Find the length of the common suffix between two strings.
        
        Args:
            str1: First string
            str2: Second string
            
        Returns:
            The length of the common suffix
        """
        # Reverse both strings to check from the end
        str1_rev = str1[::-1]
        str2_rev = str2[::-1]
        min_len = min(len(str1_rev), len(str2_rev))
        common = 0
        
        # Count matching characters from the end
        for i in range(min_len):
            if str1_rev[i] == str2_rev[i]:
                common += 1
            else:
                break
                
        return common

    # Get common suffix length and trim both strings
    suffix_len = find_common_suffix_length(s1, s2)
    s1_trimmed = s1[:-suffix_len] if suffix_len > 0 else s1
    s2_trimmed = s2[:-suffix_len] if suffix_len > 0 else s2

    # Find the starting point of differences in the trimmed strings
    min_length = min(len(s1_trimmed), len(s2_trimmed))
    diff_start_index: Optional[int] = None
    
    # Scan for the first difference
    for i in range(min_length):
        if s1_trimmed[i] != s2_trimmed[i]:
            diff_start_index = i
            break
    
    # If no differences found in the common part, check if one string is longer
    if diff_start_index is None:
        # If strings have different lengths, return the extra parts
        if len(s1_trimmed) != len(s2_trimmed):
            return (s1_trimmed[min_length:], s2_trimmed[min_length:])
        # If strings are identical, return empty strings
        else:
            return ('', '')

    # Set the end index to the end of the shorter string
    diff_end_index = min_length - 1
    
    # Return the differing parts
    return (
        s1_trimmed[diff_start_index:diff_end_index+1], 
        s2_trimmed[diff_start_index:diff_end_index+1]
    )

if __name__ == '__main__':
    # Test cases
    test_cases = [
        ("I_BUKRS(D06,V_1710)", "I_BUKRS(D06,V_7Y10)"),  # Expected: ('10, '7Y')
        ("ABC123XYZ", "DEF123XYZ"),                      # Expected: ('ABC', 'DEF')
        ("File_v1.data", "File_v2.data")                 # Expected: ('v1', 'v2')
    ]
    
    for s1, s2 in test_cases:
        result = extract_diff_parts(s1, s2)
        print(f"Comparing '{s1}' and '{s2}': {result}")