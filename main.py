from common.config import get_system_client
from starter_pack_compare.sol_build import SolB<PERSON>
from starter_pack_compare.download_vifles import download_all_country_files


if __name__ == "__main__":
    # Download all country files
    download_all_country_files()

    # Initialize SolBuild instance with system client from config
    sol_build = SolBuild(get_system_client())

    # Execute full processing pipeline
    df_img_subobj_file, df_ecat_file, df_bcs_file = sol_build.join_dataframes()
    df_img_sobj_definition = sol_build.get_sobj_definition()
    df_bc_field_definition = sol_build.get_bc_field_definition()
    df_ecat_fld_definition = sol_build.get_ecat_fld_definition()

    # Perform cross-country file comparisons
    sol_build.compare_files_across_countries_for_img(df_img_subobj_file, df_img_sobj_definition)
    sol_build.compare_files_across_countries_for_ecat(df_ecat_file, df_ecat_fld_definition)
    sol_build.compare_files_across_countries_for_bcs(df_bcs_file, df_bc_field_definition)
    