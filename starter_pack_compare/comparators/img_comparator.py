from typing import Dict, List
import pandas as pd

from .base_comparator import BaseComparator

class IMGComparator(BaseComparator):
    """Comparator for IMG (Implementation Guide) files.
    
    This class handles the comparison of IMG files across different countries,
    using the sub-object definition to determine keys for comparison.
    """
    
    def __init__(self, subobj_definition: pd.DataFrame):
        """Initialize the IMG comparator.
        
        Args:
            subobj_definition: DataFrame containing sub-object definitions
        """
        super().__init__()
        self.subobj_definition = subobj_definition

    def get_keys(self, img_activity: str, cust_objtype: str, 
                cust_objname: str, subobjname: str) -> List[str]:
        """Get keys for a specific IMG combination.
        
        Args:
            img_activity: IMG activity
            cust_objtype: Custom object type
            cust_objname: Custom object name
            subobjname: Sub-object name
            
        Returns:
            List of keys for the combination
        """
        mask = (
            (self.subobj_definition['img_activity'] == img_activity) &
            (self.subobj_definition['cust_objtype'] == cust_objtype) &
            (self.subobj_definition['cust_objname'] == cust_objname) &
            (self.subobj_definition['subobjname'] == subobjname) &
            (self.subobj_definition['keyflag'] == 'X')
        )
        return self.subobj_definition[mask]['param'].tolist()

    def get_non_keys(self, img_activity: str, cust_objtype: str, 
                    cust_objname: str, subobjname: str) -> List[str]:
        """Get non-key fields for a specific IMG combination.
        
        Args:
            img_activity: IMG activity
            cust_objtype: Custom object type
            cust_objname: Custom object name
            subobjname: Sub-object name
            
        Returns:
            List of non-key fields for the combination
        """
        mask = (
            (self.subobj_definition['img_activity'] == img_activity) &
            (self.subobj_definition['cust_objtype'] == cust_objtype) &
            (self.subobj_definition['cust_objname'] == cust_objname) &
            (self.subobj_definition['subobjname'] == subobjname) &
            (self.subobj_definition['keyflag'] == '')
        )
        return self.subobj_definition[mask]['param'].tolist()

    def compare_files(self, img_records: pd.DataFrame) -> None:
        """Compare IMG files across different countries.
        
        Args:
            img_records: DataFrame containing IMG file information
        """
        # Initialize HTML output
        self.init_html_output('IMG')
        # Get DE records as reference
        de_records = img_records[img_records['land1'] == self.reference_country]
        
        # Process each DE record
        for _, de_row in de_records.iterrows():
            # Get keys for this combination
            keys = self.get_keys(
                de_row['img_activity'],
                de_row['cust_objtype'],
                de_row['cust_objname'],
                de_row['subobjname']
            )
            
            if not keys:
                continue
                
            # Read DE file content
            de_file_path = self.get_file_path(self.reference_country, de_row['filename_on_sobj_level'])
            de_content = self.read_file_content(de_file_path)
            
            if not de_content:
                continue
                
            # Find matching records in other countries
            de_bbid_base = self.get_base_bbid(de_row['bbid'])
            matching_records = img_records[
                (img_records['land1'].isin(self.other_countries))&
                (img_records['bbid'].str.extract(r'^(.+?)\s*\(', expand=False) == de_bbid_base) &
                (img_records['img_activity'] == de_row['img_activity']) &
                (img_records['cust_objtype'] == de_row['cust_objtype']) &
                (img_records['cust_objname'] == de_row['cust_objname']) &
                (img_records['subobjname'] == de_row['subobjname'])
            ]
            
            if matching_records.empty:
                continue

            self.print_comparison_header(
                'IMG',
                de_row['bbid'],
                de_row['activity_type'],
                de_row['activity_id'],
                de_row['filename_on_sobj_level'],
                keys
            )
            
            # Compare with each matching record
            for _, other_row in matching_records.iterrows():
                other_file_path = self.get_file_path(other_row['land1'], other_row['filename_on_sobj_level'])
                other_content = self.read_file_content(other_file_path)
                
                if not other_content:
                    continue
                
                # First check if key values match
                key_values_match = True
                for key in keys:
                    if key not in de_content or key not in other_content:
                        key_values_match = False
                        break
                    if not self.is_equal(de_content[key], other_content[key], other_row['land1']):
                        key_values_match = False
                        break
                
                if not key_values_match:
                    continue
                
                self.print_matching_files(
                    de_row['filename_on_sobj_level'],
                    other_row['land1'],
                    other_row['filename_on_sobj_level'],
                    other_row['bbid']
                )
                
                # Compare all other fields and add to HTML
                non_keys = self.get_non_keys(
                    de_row['img_activity'],
                    de_row['cust_objtype'],
                    de_row['cust_objname'],
                    de_row['subobjname']
                )
                self.print_field_differences_with_html(
                    de_content, other_content, other_row['land1'], keys, non_keys,
                    de_row['bbid'], de_row['activity_type'], de_row['activity_id'],
                    de_row['filename_on_sobj_level'], other_row['filename_on_sobj_level'], other_row['bbid']
                )
        
        # Save HTML output at the end
        self.save_html_output()