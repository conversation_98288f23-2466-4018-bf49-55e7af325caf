from typing import Dict, List
import re
from pathlib import Path
from datetime import datetime
from enum import Enum

from common.paths import paths
from common.logger import logger
from core.utils.extract_diff_parts import extract_diff_parts
from common.config import get_starter_pack_country_codes, get_source_country_code


class ComparisonResult(Enum):
    EQUAL = 0  # 完全相等
    EXPECTED_DIFF = 1  # 预期的差异
    UNEXPECTED_DIFF = 2  # 非预期的差异

class BaseComparator:
    """Base class for all file comparators.
    
    This class provides common functionality for comparing files across different countries,
    including file reading, value comparison, and key matching.
    """
    

    from common.config import config

    EXPECTED_DIFFERENCES = config.get('expectedDifferences', {})

    def __init__(self, reference_country: str = get_source_country_code()):
        """Initialize the comparator.
        
        Args:
            reference_country: The country code to use as reference (default: 'DE')
        """
        self.record_type = None
        self.reference_country = reference_country
        self.other_countries = get_starter_pack_country_codes()
        # self.other_countries = ['BS'] ## TODO-DEBUG te be removed, to simplify debugging
        self.html_content = []
        self.current_comparison = None

    @staticmethod
    def read_file_content(file_path: str) -> Dict[str, str]:
        """Read and parse file content.
        
        Args:
            file_path: Path to the file to read
            
        Returns:
            Dictionary containing parameter-value pairs from the file
        """
        try:
            with open(file_path, 'r', encoding='utf-16') as f:
                lines = f.readlines()
            if len(lines) >= 4:
                # First line contains parameters
                params = lines[0].strip().split('\t')[2:]  # Skip first two elements
                # Fourth line contains values
                values = lines[3].strip().split('\t')[2:]  # Skip first two elements
                
                # Ensure values length matches params length
                while len(values) < len(params):
                    values.append('')
                
                return dict(zip(params, values))
            return {}
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {str(e)}")
            return {}

    def compare_values(self, val1: str, val2: str, other_country: str = None) -> ComparisonResult:
        """Compare two values with special handling for organization values.
        
        Args:
            val1: First value to compare (DE value)
            val2: Second value to compare (other country value)
            other_country: Country code of the other value (for expected differences)
            
        Returns:
            ComparisonResult indicating if values are equal, have expected differences,
            or have unexpected differences
        """
        # Handle empty values
        if val1 == '' and val2 == '':
            return ComparisonResult.EQUAL
        if val1 == '' or val2 == '':
            return ComparisonResult.UNEXPECTED_DIFF
            
        if val1 == val2:
            return ComparisonResult.EQUAL

        # 使用extract_diff_parts获取差异部分
        diff1, diff2 = extract_diff_parts(val1, val2)
        
        # 如果没有差异，返回相等
        if not diff1 and not diff2:
            return ComparisonResult.EQUAL
            
        # 如果有国家信息，检查是否是预期的差异
        if other_country:
            for column_diffs in self.EXPECTED_DIFFERENCES.values():
                if diff1 in column_diffs:
                    country_diffs = column_diffs[diff1]
                    if other_country in country_diffs and country_diffs[other_country] == diff2:
                        return ComparisonResult.EXPECTED_DIFF
                    
        # 如果值不相等，且不是预期的差异，则返回非预期差异
        if val1 != val2:
            return ComparisonResult.UNEXPECTED_DIFF
            
        return ComparisonResult.EQUAL

    def is_equal(self, val1: str, val2: str, other_country: str = None) -> bool:
        """Helper method to check if values are equal (for backward compatibility).
        
        Args:
            val1: First value to compare
            val2: Second value to compare
            other_country: Country code of the other value
            
        Returns:
            True if values are equal or have expected differences, False otherwise
        """
        result = self.compare_values(val1, val2, other_country)
        return result in [ComparisonResult.EQUAL, ComparisonResult.EXPECTED_DIFF]

    @staticmethod
    def get_base_bbid(bbid: str) -> str:
        """Extract the base BBID from a BBID string.
        
        Args:
            bbid: The BBID string to process
            
        Returns:
            The base BBID (part before the country code in parentheses)
        """
        match = re.match(r'^(.+?)\s*\(', bbid)
        return match.group(1) if match else bbid

    @staticmethod
    def get_file_path(country: str, filename: str) -> str:
        """Get the full file path for a given country and filename.
        
        Args:
            country: Country code
            filename: Name of the file
            
        Returns:
            Full path to the file
        """
        return str(Path(paths.vfile_dir) / country / 'Model_S' / filename)

    @staticmethod
    def print_comparison_header(record_type: str, bbid: str, activity_type: str,
                              activity_id: str, filename: str, keys: List[str]) -> None:
        """Print the header for a comparison.
        
        Args:
            record_type: Type of record being compared (e.g., 'IMG', 'ECAT', 'BCS')
            bbid: BBID of the record
            activity_type: Type of activity
            activity_id: ID of the activity
            filename: Name of the file
            keys: List of keys used for comparison
        """
        print(f"\n>>>>>>>>>>Comparing {record_type} files for:")
        print(f"BBID: {bbid}")
        print(f"Activity Type: {activity_type}")
        print(f"Activity ID: {activity_id}")
        print(f"DE File: {filename}")
        print(f"Keys: {keys}")

    @staticmethod
    def print_matching_files(de_filename: str, other_country: str, 
                           other_filename: str, other_bbid: str) -> None:
        """Print information about matching files.
        
        Args:
            de_filename: Name of the DE file
            other_country: Country code of the other file
            other_filename: Name of the other file
            other_bbid: BBID of the other record
        """
        print(f"\nFound matching key values between:")
        print(f"DE File: {de_filename}")
        print(f"{other_country} File: {other_filename}")
        print(f"BBID: {other_bbid}")

    def print_field_differences(self, de_content: Dict[str, str], other_content: Dict[str, str],
                              other_country: str, keys: List[str]) -> None:
        """Print differences between fields in two files.
        
        Args:
            de_content: Content of the DE file
            other_content: Content of the other file
            other_country: Country code of the other file
            keys: List of keys to exclude from comparison
        """
        print("\nDifferences in other fields:")
        
        # Get all unique fields from both contents
        all_fields = set(de_content.keys()) | set(other_content.keys())
        
        for param in all_fields:
            if param not in keys:  # Skip key fields
                if param in de_content and param in other_content:
                    # Both files have this field, compare values
                    if not self.compare_values(de_content[param], other_content[param]):
                        print(f"Field: {param}")
                        print(f"  DE: {de_content[param] if de_content[param] != '' else '(empty)'}")
                        print(f"  {other_country}: {other_content[param] if other_content[param] != '' else '(empty)'}")
                elif param in de_content:
                    # Field only in DE
                    print(f"Field {param} missing in {other_country}")
                else:
                    # Field only in other country
                    print(f"Field {param} missing in DE")
    
    def init_html_output(self, record_type: str) -> None:
        """Initialize HTML output for a comparison session.
        
        Args:
            record_type: Type of record being compared (e.g., 'IMG', 'ECAT', 'BCS')
        """
        self.html_content = []
        self.record_type = record_type
        
        # HTML header
        html_header = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{record_type} 文件比较结果</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: none;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }}
        .comparison-section {{
            margin-bottom: 40px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }}
        .section-header {{
            background-color: #43a39d;
            color: white;
            padding: 15px;
            margin: 0;
            font-size: 18px;
        }}
        .file-info {{
            background-color: #f9f9f9;
            padding: 10px 15px;
            border-bottom: 1px solid #ddd;
            font-size: 14px;
        }}
        .comparison-table {{
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }}
        .table-container {{
            overflow-x: auto;
            margin: 0;
        }}
        .comparison-table th {{
            background-color: #f0f0f0;
            padding: 8px;
            text-align: left;
            border: 1px solid #ddd;
            font-weight: bold;
        }}
        .comparison-table td {{
            padding: 6px 8px;
            border: 1px solid #ddd;
            vertical-align: top;
            word-break: break-all;
        }}
        .header-row {{
            background-color: #e8f5e8;
        }}
        .de-row {{
            background-color: #e3f2fd;
        }}
        .other-row {{
            background-color: #fff3e0;
        }}
        .key-field {{
            text-decoration: underline;
            font-weight: bold;
        }}
        .different-value {{
            background-color: #fff9b3;
            font-weight: bold;
        }}
        .expected-diff {{
            background-color: #fff9b3;
            font-weight: bold;
        }}
        .unexpected-diff {{
            background-color: #ffcdd2;
            font-weight: bold;
        }}
        .missing-field {{
            background-color: #a9a9a9;
            font-style: italic;
            color: #666;
        }}
        .empty-value {{
            color: #999;
            font-style: italic;
        }}
        .timestamp {{
            text-align: center;
            color: #666;
            font-size: 12px;
            margin-top: 20px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{record_type} 文件比较结果</h1>
"""
        self.html_content.append(html_header)
    
    def add_comparison_to_html(self, bbid: str, activity_type: str, activity_id: str, 
                              de_filename: str, other_country: str, other_filename: str, 
                              other_bbid: str, de_content: Dict[str, str], 
                              other_content: Dict[str, str], keys: List[str],
                              non_keys: List[str]) -> None:
        """Add a file comparison to HTML output.
        
        Args:
            bbid: BBID of the DE record
            activity_type: Type of activity
            activity_id: ID of the activity
            de_filename: Name of the DE file
            other_country: Country code of the other file
            other_filename: Name of the other file
            other_bbid: BBID of the other record
            de_content: Content of the DE file
            other_content: Content of the other file
            keys: List of keys used for comparison
            non_keys: List of non-key fields to check for differences
        """
        # Get all unique fields and sort them (keys first)
        all_fields = set(de_content.keys()) | set(other_content.keys())
        key_fields = [field for field in all_fields if field in keys]
        # non_key_fields = [field for field in all_fields if field in non_keys]  ## todo 如果使用这个，所有的text in each languages会丢掉
        non_key_fields = [field for field in all_fields if field not in keys]
        sorted_fields = key_fields + sorted(non_key_fields)
        
        # Check if there are any differences in non-key fields
        has_differences = False
        for param in non_key_fields:  # Only check non-key fields
            # Check if param is in non_keys directly or after removing language suffix
            base_param = re.sub(r'_[A-Z0-9]{2}$', '', param)    ## TODO 其实还要确定是text字段
            if param in non_keys or base_param in non_keys:
                if param in de_content and param in other_content:
                    val1 = de_content[param]
                    val2 = other_content[param]
                    # Skip if both values are empty
                    if val1 == '' and val2 == '':
                        continue
                    # Check if values are different
                    if val1 != val2:
                        result = self.compare_values(val1, val2, other_country)
                        if result in [ComparisonResult.EXPECTED_DIFF, ComparisonResult.UNEXPECTED_DIFF]:
                            has_differences = True
                            break
                elif param in de_content or param in other_content:
                    # 如果字段在non_keys中定义但在某个文件中缺失，这是差异
                    has_differences = True
                    break
            # 如果字段(或去掉语言后缀之后的param)只在non_key_fields中而不在non_keys中，不算差异
        
        # Only add to HTML if there are differences in non-key fields
        if not has_differences:
            return
            
        # Section header
        section_html = f"""
        <div class="comparison-section">
            <h2 class="section-header">比较: {bbid} vs {other_bbid}</h2>
            <div class="file-info">
                <strong>Activity Type:</strong> {activity_type} | 
                <strong>Activity ID:</strong> {activity_id} | 
                <strong>Keys:</strong> {', '.join(keys)}<br>
                <strong>DE File:</strong> {de_filename}<br>
                <strong>{other_country} File:</strong> {other_filename}
            </div>
"""
        
        # Create table
        table_html = '<table class="comparison-table">'
        
        # Header row with parameters
        table_html += '<tr class="header-row">'
        table_html += '<th>Country</th>'
        for field in sorted_fields:
            field_class = 'key-field' if field in keys else ''
            table_html += f'<th class="{field_class}">{field}</th>'
        table_html += '</tr>'
        
        # DE row
        table_html += '<tr class="de-row">'
        table_html += '<td><strong>DE</strong></td>'
        for field in sorted_fields:
            value = de_content.get(field, '')
            display_value = value if value != '' else '(empty)'
            value_class = 'empty-value' if value == '' else ''
            
            # Check if this field is different (only for non-key fields)
            if field not in keys:
                base_field = re.sub(r'_[A-Z0-9]{2}$', '', field) ## TODO 其实还要确定是text字段
                if field in non_keys or base_field in non_keys:  # 只对non_keys中定义的字段(包含去掉language suffix之后后)进行比较
                    if field in other_content:
                        result = self.compare_values(value, other_content[field], other_country)
                        if result == ComparisonResult.EXPECTED_DIFF:
                            value_class += ' expected-diff'
                        elif result == ComparisonResult.UNEXPECTED_DIFF:
                            value_class += ' unexpected-diff'
                    elif field not in other_content:  # 如果字段在non_keys中但在other_content中缺失
                        value_class += ' missing-field'  # 用灰色高亮缺失字段
            
            field_class = 'key-field' if field in keys else ''
            table_html += f'<td class="{field_class} {value_class}">{display_value}</td>'
        table_html += '</tr>'
        
        # Other country row
        table_html += f'<tr class="other-row">'
        table_html += f'<td><strong>{other_country}</strong></td>'
        for field in sorted_fields:
            value = other_content.get(field, '')
            display_value = value if value != '' else '(empty)'
            value_class = 'empty-value' if value == '' else ''
            
            # Check if this field is different (only for non-key fields)
            if field not in keys:
                base_field = re.sub(r'_[A-Z0-9]{2}$', '', field)    ## TODO 其实还要确定是text字段
                if field in non_keys or base_field in non_keys:  # 只对non_keys中定义的字段进行比较
                    if field in de_content:
                        result = self.compare_values(de_content[field], value, other_country)
                        if result == ComparisonResult.EXPECTED_DIFF:
                            value_class += ' expected-diff'
                        elif result == ComparisonResult.UNEXPECTED_DIFF:
                            value_class += ' unexpected-diff'
                    elif field not in de_content:  # 如果字段在non_keys中但在de_content中缺失
                        value_class += ' missing-field'  # 用灰色高亮缺失字段
            
            field_class = 'key-field' if field in keys else ''
            table_html += f'<td class="{field_class} {value_class}">{display_value}</td>'
        table_html += '</tr>'
        
        table_html += '</table></div>'
        
        section_html += f'<div class="table-container">{table_html}</div>'
        self.html_content.append(section_html)
    
    def save_html_output(self) -> None:
        """Save the HTML content to a file."""
        if not self.html_content:
            return
            
        # HTML footer
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        html_footer = f"""
        <div class="timestamp">
            生成时间: {timestamp}
        </div>
    </div>
</body>
</html>
"""
        self.html_content.append(html_footer)
        
        # Create output directory if it doesn't exist
        output_dir = Path(paths.data_dir) / 'output'
        output_dir.mkdir(exist_ok=True)
        
        # Generate filename with timestamp
        timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'{self.record_type}_comparison_{timestamp_str}.html'
        output_path = output_dir / filename
        
        # Write HTML file
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(''.join(self.html_content))
            logger.info(f'HTML comparison report saved to: {output_path}')
            print(f'\nHTML comparison report saved to: {output_path}')
        except Exception as e:
            logger.error(f'Error saving HTML file: {str(e)}')
            print(f'Error saving HTML file: {str(e)}')
    
    def print_field_differences_with_html(self, de_content: Dict[str, str], other_content: Dict[str, str],
                                         other_country: str, keys: List[str], non_keys: List[str],
                                         bbid: str, activity_type: str, activity_id: str,
                                         de_filename: str, other_filename: str, other_bbid: str) -> None:
        """Print differences between fields in two files and add to HTML output.
        
        Args:
            de_content: Content of the DE file
            other_content: Content of the other file
            other_country: Country code of the other file
            keys: List of keys to exclude from comparison
            non_keys: List of non-key fields to check for differences
            bbid: BBID of the DE record
            activity_type: Type of activity
            activity_id: ID of the activity
            de_filename: Name of the DE file
            other_filename: Name of the other file
            other_bbid: BBID of the other record
        """
        # Keep original print functionality
        self.print_field_differences(de_content, other_content, other_country, keys)
        
        # Add to HTML output
        self.add_comparison_to_html(bbid, activity_type, activity_id, de_filename, 
                                   other_country, other_filename, other_bbid, 
                                   de_content, other_content, keys, non_keys)
                    