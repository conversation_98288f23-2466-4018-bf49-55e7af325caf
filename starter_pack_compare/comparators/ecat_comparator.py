from typing import Dict, List
import pandas as pd

from .base_comparator import BaseComparator

class ECATComparator(BaseComparator):
    """Comparator for ECAT (eCATT) files.
    
    This class handles the comparison of ECAT files across different countries,
    using the ECAT definition to determine keys for comparison.
    """
    
    def __init__(self, ecat_definition: pd.DataFrame):
        """Initialize the ECAT comparator.
        
        Args:
            ecat_definition: DataFrame containing ECAT definitions
        """
        super().__init__()
        self.ecat_definition = ecat_definition

    def get_keys(self, activity_id: str) -> List[str]:
        """Get keys for a specific ECAT activity.
        
        Args:
            activity_id: ECAT activity ID
            
        Returns:
            List of keys for the activity
        """
        mask = (
            (self.ecat_definition['objid'] == activity_id) &
            (self.ecat_definition['keyflag'] == 'X')
        )
        return self.ecat_definition[mask]['pname'].tolist()

    def get_non_keys(self, activity_id: str) -> List[str]:
        """Get non-key fields for a specific ECAT activity.
        
        Args:
            activity_id: ECAT activity ID
            
        Returns:
            List of non-key fields for the activity
        """
        mask = (
            (self.ecat_definition['objid'] == activity_id) &
            (self.ecat_definition['keyflag'] == '')
        )
        return self.ecat_definition[mask]['pname'].tolist()

    def compare_files(self, ecat_records: pd.DataFrame) -> None:
        """Compare ECAT files across different countries.
        
        Args:
            ecat_records: DataFrame containing ECAT file information
        """
        # Initialize HTML output
        self.init_html_output('ECAT')
        # Get DE records as reference
        de_records = ecat_records[ecat_records['land1'] == self.reference_country]
        
        # Process each DE record
        for _, de_row in de_records.iterrows():
            # Get keys for this combination
            keys = self.get_keys(de_row['activity_id'])
            
            if not keys:
                continue
                
            # Read DE file content
            de_file_path = self.get_file_path(self.reference_country, de_row['filename'])
            de_content = self.read_file_content(de_file_path)
            
            if not de_content:
                continue
                
            # Find matching records in other countries
            de_bbid_base = self.get_base_bbid(de_row['bbid'])
            matching_records = ecat_records[
                (ecat_records['bbid'].str.extract(r'^(.+?)\s*\(', expand=False) == de_bbid_base) &
                (ecat_records['activity_type'] == de_row['activity_type']) &
                (ecat_records['activity_id'] == de_row['activity_id']) &
                (ecat_records['land1'].isin(self.other_countries))
            ]
            
            if matching_records.empty:
                continue
                
            self.print_comparison_header(
                'ECAT',
                de_row['bbid'],
                de_row['activity_type'],
                de_row['activity_id'],
                de_row['filename'],
                keys
            )
            
            # Compare with each matching record
            for _, other_row in matching_records.iterrows():
                other_file_path = self.get_file_path(other_row['land1'], other_row['filename'])
                other_content = self.read_file_content(other_file_path)
                
                if not other_content:
                    continue
                
                # First check if key values match
                key_values_match = True
                for key in keys:
                    if key not in de_content or key not in other_content:
                        key_values_match = False
                        break
                    if not self.is_equal(de_content[key], other_content[key], other_row['land1']):
                        key_values_match = False
                        break
                
                if not key_values_match:
                    continue
                
                self.print_matching_files(
                    de_row['filename'],
                    other_row['land1'],
                    other_row['filename'],
                    other_row['bbid']
                )
                
                # Compare all other fields and add to HTML
                non_keys = self.get_non_keys(de_row['activity_id'])
                self.print_field_differences_with_html(
                    de_content, other_content, other_row['land1'], keys, non_keys,
                    de_row['bbid'], de_row['activity_type'], de_row['activity_id'],
                    de_row['filename'], other_row['filename'], other_row['bbid']
                )
        
        # Save HTML output at the end
        self.save_html_output()